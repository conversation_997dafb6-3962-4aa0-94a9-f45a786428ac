#!/usr/bin/env python3
"""
测试合并后的应用
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        from backend.app.main import app
        print("✅ 主应用导入成功")
    except Exception as e:
        print(f"❌ 主应用导入失败: {e}")
        return False
    
    try:
        from backend.app.cms.database import init_database
        print("✅ CMS数据库模块导入成功")
    except Exception as e:
        print(f"❌ CMS数据库模块导入失败: {e}")
        return False
    
    try:
        from backend.app.cms.routes import router
        print("✅ CMS路由模块导入成功")
    except Exception as e:
        print(f"❌ CMS路由模块导入失败: {e}")
        return False
    
    try:
        from backend.app.cms.service import cms_service
        print("✅ CMS服务模块导入成功")
    except Exception as e:
        print(f"❌ CMS服务模块导入失败: {e}")
        return False
    
    return True

def test_routes():
    """测试路由"""
    print("\n🔍 测试路由集成...")
    
    try:
        from backend.app.main import app
        
        # 获取所有路由
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        print(f"✅ 总路由数量: {len(routes)}")
        
        # 检查RAG路由
        rag_routes = [r for r in routes if r.startswith('/api/') and not r.startswith('/api/cms')]
        print(f"✅ RAG路由数量: {len(rag_routes)}")
        
        # 检查CMS路由
        cms_routes = [r for r in routes if r.startswith('/api/cms')]
        print(f"✅ CMS路由数量: {len(cms_routes)}")
        
        if cms_routes:
            print("✅ CMS路由示例:")
            for route in cms_routes[:5]:  # 显示前5个
                print(f"   - {route}")
        
        return len(cms_routes) > 0
        
    except Exception as e:
        print(f"❌ 路由测试失败: {e}")
        return False

def test_database():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    try:
        from backend.app.cms.database import init_database
        result = init_database()
        
        if result:
            print("✅ 数据库连接成功")
        else:
            print("⚠️  数据库连接失败（这是正常的，如果没有配置数据库）")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试合并后的应用...")
    print("=" * 50)
    
    success = True
    
    # 测试导入
    if not test_imports():
        success = False
    
    # 测试路由
    if not test_routes():
        success = False
    
    # 测试数据库
    if not test_database():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！合并成功！")
        print("\n📋 合并总结:")
        print("✅ 9001端口的pipeline后端已成功合并到9000端口")
        print("✅ CMS功能已集成到主应用中")
        print("✅ 数据库连接模块已集成")
        print("✅ API路由已统一")
        print("✅ 配置文件已合并")
        print("\n🚀 现在可以使用以下命令启动应用:")
        print("   python start.py")
        print("   或者")
        print("   uvicorn backend.app.main:app --host 0.0.0.0 --port 9000 --reload")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
