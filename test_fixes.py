#!/usr/bin/env python3
"""
测试修复后的问题
"""
import sys
import os
import warnings
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chroma_config():
    """测试ChromaDB配置是否修复"""
    print("🔍 测试ChromaDB配置...")
    
    try:
        # 捕获警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            from backend.app.rag_service import RAGService
            
            # 检查是否有ChromaDB相关的警告
            chroma_warnings = [warning for warning in w 
                             if "deprecated configuration" in str(warning.message).lower() 
                             or "legacy_error" in str(warning.message).lower()]
            
            if chroma_warnings:
                print("❌ ChromaDB配置仍有警告:")
                for warning in chroma_warnings:
                    print(f"   - {warning.message}")
                return False
            else:
                print("✅ ChromaDB配置正常，无过时警告")
                return True
                
    except Exception as e:
        print(f"❌ ChromaDB配置测试失败: {e}")
        return False

def test_database_config():
    """测试数据库配置是否修复"""
    print("\n🔍 测试数据库配置...")
    
    try:
        from backend.app.cms.database import get_database_url, init_database
        
        # 测试数据库URL生成
        db_url = get_database_url()
        if db_url:
            print("✅ 数据库URL生成成功")
            print(f"   URL: {db_url}")
            
            # 测试数据库初始化
            result = init_database()
            if result:
                print("✅ 数据库连接成功")
            else:
                print("⚠️  数据库连接失败（可能是数据库服务未启动，这是正常的）")
            
            return True
        else:
            print("❌ 数据库URL生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库配置测试失败: {e}")
        return False

def test_app_startup():
    """测试应用启动"""
    print("\n🔍 测试应用启动...")
    
    try:
        # 捕获所有输出和警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            from backend.app.main import app
            
            # 检查是否有严重警告
            serious_warnings = [warning for warning in w 
                              if "error" in str(warning.message).lower() 
                              or "failed" in str(warning.message).lower()]
            
            if serious_warnings:
                print("⚠️  应用启动有警告:")
                for warning in serious_warnings:
                    print(f"   - {warning.message}")
            else:
                print("✅ 应用启动正常")
            
            # 检查路由数量
            route_count = len(app.routes)
            print(f"✅ 路由数量: {route_count}")
            
            return True
            
    except Exception as e:
        print(f"❌ 应用启动测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试修复结果...")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # 测试ChromaDB配置
    if test_chroma_config():
        success_count += 1
    
    # 测试数据库配置
    if test_database_config():
        success_count += 1
    
    # 测试应用启动
    if test_app_startup():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有问题已修复！")
        print("\n📋 修复总结:")
        print("✅ 问题1: ChromaDB过时配置警告已修复")
        print("   - 移除了过时的 chroma_db_impl 配置")
        print("   - 更新了相关文档和配置文件")
        print("✅ 问题2: 数据库配置警告已修复")
        print("   - CMS模块现在正确读取settings配置")
        print("   - 数据库连接信息可以正确获取")
        print("\n🚀 现在可以正常启动应用:")
        print("   python start.py")
        print("   或者")
        print("   uvicorn backend.app.main:app --host 0.0.0.0 --port 9000 --reload")
    else:
        print("❌ 部分问题仍需解决，请检查上述错误信息")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
