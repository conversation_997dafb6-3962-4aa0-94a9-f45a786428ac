#!/usr/bin/env python3
"""
验证修复结果
"""
import sys
import os
import warnings
import subprocess
import time

def test_startup_warnings():
    """测试启动时的警告信息"""
    print("🔍 测试应用启动警告...")
    
    try:
        # 启动应用并捕获输出
        cmd = [
            sys.executable, "-c",
            "import warnings; from backend.app.main import app; print('STARTUP_SUCCESS')"
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            cwd=os.getcwd()
        )
        
        output = result.stdout + result.stderr
        
        # 检查是否有ChromaDB过时配置警告
        if "deprecated configuration" in output.lower() or "legacy_error" in output.lower():
            print("❌ 仍有ChromaDB过时配置警告")
            print("输出:", output)
            return False
        
        # 检查是否有数据库密码未设置警告
        if "数据库密码未设置" in output:
            print("❌ 仍有数据库密码未设置警告")
            print("输出:", output)
            return False
        
        # 检查是否有数据库配置不完整警告
        if "数据库配置不完整" in output:
            print("❌ 仍有数据库配置不完整警告")
            print("输出:", output)
            return False
        
        # 检查是否成功启动
        if "STARTUP_SUCCESS" in output:
            print("✅ 应用启动成功，无相关警告")
            return True
        else:
            print("❌ 应用启动失败")
            print("输出:", output)
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_database_config():
    """测试数据库配置读取"""
    print("\n🔍 测试数据库配置读取...")
    
    try:
        from backend.app.cms.database import get_database_url
        
        db_url = get_database_url()
        if db_url and "root:5Secsgo100@localhost:3306/chestnut_cms" in db_url:
            print("✅ 数据库配置读取正常")
            return True
        else:
            print(f"❌ 数据库配置读取异常: {db_url}")
            return False
            
    except Exception as e:
        print(f"❌ 数据库配置测试失败: {e}")
        return False

def test_cms_routes():
    """测试CMS路由是否正确集成"""
    print("\n🔍 测试CMS路由集成...")
    
    try:
        from backend.app.main import app
        
        # 获取所有路由
        routes = [route.path for route in app.routes if hasattr(route, 'path')]
        cms_routes = [r for r in routes if r.startswith('/api/cms')]
        
        if len(cms_routes) >= 5:  # 应该有多个CMS路由
            print(f"✅ CMS路由集成正常，共 {len(cms_routes)} 个路由")
            return True
        else:
            print(f"❌ CMS路由集成异常，只有 {len(cms_routes)} 个路由")
            return False
            
    except Exception as e:
        print(f"❌ CMS路由测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 验证修复结果...")
    print("=" * 50)
    
    tests = [
        ("启动警告测试", test_startup_warnings),
        ("数据库配置测试", test_database_config),
        ("CMS路由测试", test_cms_routes),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有问题已修复！")
        print("\n📋 修复总结:")
        print("✅ 问题1: ChromaDB过时配置警告 - 已修复")
        print("✅ 问题2: 数据库配置读取警告 - 已修复")
        print("\n🚀 现在可以正常启动应用:")
        print("   python start.py")
        print("   或者")
        print("   uvicorn backend.app.main:app --host 0.0.0.0 --port 9000 --reload")
        print("\n💡 注意:")
        print("   - 如果没有MySQL数据库，CMS功能将不可用，但RAG功能正常")
        print("   - 数据库连接失败是正常的（如果MySQL服务未启动）")
    else:
        print("❌ 部分问题仍需解决")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
